import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowUpIcon, FileText, FileImage, Globe } from "lucide-react";
import { AutoResizeTextarea } from "@/components/ui/autoresize-textarea";
import { useLanguage } from "@/lib/language-context";
import { Message, ImageAttachment, FileAttachment } from "../types";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Upload,
  Plus,
  FileAudio,
  FileSpreadsheet,
  File as FileIcon,
  X,
  Loader2
} from "lucide-react";
import { ImageUpload } from "./ImageUpload";
import { FileUpload } from "./FileUpload";
import { cn } from "@/lib/utils";

interface ChatInputFormProps {
  input: string;
  setInput: (input: string) => void;
  handleKeyDown: (e: any) => void;
  handleChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  handleAISubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  setMessages: React.Dispatch<React.SetStateAction<Message[]>>;
  status: string;
  state: string;
  isMobile: boolean;
  includeWebResults: boolean;
  setIncludeWebResults: (include: boolean) => void;
  webSearchLimitExceeded?: boolean;
  selectedImages: ImageAttachment[];
  setSelectedImages: (images: ImageAttachment[]) => void;
  selectedFiles?: FileAttachment[];
  setSelectedFiles?: (files: FileAttachment[]) => void;
  useNewFileUpload?: boolean; // Flag to enable new file upload system
}

export const ChatInputForm: React.FC<ChatInputFormProps> = ({
  input,
  setInput,
  handleKeyDown,
  handleChange,
  handleAISubmit,
  setMessages,
  status,
  includeWebResults,
  setIncludeWebResults,
  webSearchLimitExceeded,
  selectedImages,
  setSelectedImages,
  selectedFiles = [],
  setSelectedFiles,
  useNewFileUpload = false,
}) => {
  const { t } = useLanguage();

  const handleImagesSelected = (images: ImageAttachment[]) => {
    setSelectedImages(images);
  };

  const handleRemoveImage = (imageId: string) => {
    setSelectedImages(selectedImages.filter((img) => img.id !== imageId));
  };

  const handleFilesSelected = (files: FileAttachment[]) => {
    if (setSelectedFiles) {
      setSelectedFiles(files);
    }
  };

  const handleRemoveFile = (fileId: string) => {
    if (setSelectedFiles) {
      setSelectedFiles(selectedFiles.filter((file) => file.id !== fileId));
    }
  };

  const getFileIcon = (fileType: string) => {
    switch (fileType) {
      case 'image':
        return <FileImage className="h-4 w-4" />;
      case 'audio':
        return <FileAudio className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'text':
        return <FileText className="h-4 w-4" />;
      case 'spreadsheet':
        return <FileSpreadsheet className="h-4 w-4" />;
      default:
        return <FileIcon className="h-4 w-4" />;
    }
  };

  // Convert FileAttachment to ImageAttachment for backward compatibility
  const convertFilesToImages = (files: FileAttachment[]): ImageAttachment[] => {
    return files
      .filter(file => file.fileType === 'image')
      .map(file => ({
        id: file.id,
        url: file.url,
        name: file.name,
        type: file.type,
        size: file.size,
        preview: file.preview,
      }));
  };

  const canSubmit = input.trim() !== "" || selectedImages.length > 0 || selectedFiles.length > 0;

  return (
    <div className="flex flex-col w-full px-2 sm:px-4 md:px-6">
      {/* Web search toggle */}
      <div className="flex items-center w-full mb-2 sm:mb-3">
        <div className="px-2 sm:px-3 py-1 sm:py-1.5 bg-background/90 backdrop-blur-sm rounded-full border border-input shadow-sm transition-all duration-200 ease-linear">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="flex items-center gap-1.5 sm:gap-2">
                  <Switch
                    id="web-search"
                    checked={includeWebResults === true}
                    onCheckedChange={(checked) => {
                      console.log("Web search toggle changed to:", checked);
                      setIncludeWebResults(checked);
                    }}
                    disabled={
                      webSearchLimitExceeded ||
                      status === "streaming" ||
                      status === "submitted"
                    }
                    className={cn(
                      "scale-75 sm:scale-100",
                      webSearchLimitExceeded && "cursor-not-allowed opacity-50"
                    )}
                  />
                  <Label
                    htmlFor="web-search"
                    className="flex items-center gap-1 text-xs sm:text-sm cursor-pointer select-none"
                  >
                    <Globe className="h-3 w-3 sm:h-3.5 sm:w-3.5" />
                    <span className="hidden xs:inline">{t("chat.includeWebResults")}</span>
                    <span className="xs:hidden">Web</span>
                  </Label>
                </div>
              </TooltipTrigger>
              <TooltipContent side="top">
                {webSearchLimitExceeded
                  ? t("chat.webSearchLimitExceeded")
                  : t("chat.webSearchTooltip")}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </div>

      {/* Enhanced Image Upload Section */}
      {/* {selectedImages.length > 0 && (
        <div className="w-full max-w-[45rem] mb-4">
          <div className="bg-muted/30 rounded-xl p-4 border border-border/50">
            <div className="flex items-center gap-2 mb-3">
              <div className="bg-primary/10 rounded-full p-1">
                <ImageIcon className="h-4 w-4 text-primary" />
              </div>
              <span className="text-sm font-medium text-foreground">
                {t("chat.attachedImages") || "Attached Images"}
              </span>
              <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded-full">
                {selectedImages.length}
              </span>
            </div>
            <ImageUpload
              onImagesSelected={handleImagesSelected}
              selectedImages={selectedImages}
              onRemoveImage={handleRemoveImage}
              disabled={status === "streaming" || status === "submitted"}
            />
          </div>
        </div>
      )} */}
      {selectedFiles.length > 0 && (
        <div className="mb-2 sm:mb-3 flex flex-wrap gap-1.5 sm:gap-2">
          {selectedFiles.map((file) => (
            <div
              key={file.id}
              className="flex items-center gap-1.5 sm:gap-2 px-2 sm:px-3 py-1.5 sm:py-2 rounded-lg bg-muted/50 border border-border/50 w-fit shadow-sm transition-all duration-200"
            >
              <span className="bg-primary/10 rounded-md p-1 sm:p-1.5 flex items-center justify-center">
                {getFileIcon(file.fileType)}
              </span>
              <span className="truncate max-w-[80px] sm:max-w-[120px] md:max-w-[150px] text-xs sm:text-sm font-medium">
                {file.name}
              </span>
              <Button
                variant="ghost"
                size="icon"
                className="h-5 w-5 sm:h-6 sm:w-6 rounded-full hover:bg-destructive/10 hover:text-destructive transition-colors"
                onClick={() => handleRemoveFile(file.id)}
                disabled={status === "streaming" || status === "submitted"}
                style={{ minWidth: 0, padding: 0 }}
              >
                <X className="h-3 w-3 sm:h-4 sm:w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
      {/* Chat input form */}
      <div className="w-full flex items-center">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (!canSubmit) return;

            // Prepare message with both legacy images and new files
            const messageImages = useNewFileUpload
              ? convertFilesToImages(selectedFiles)
              : selectedImages;

            setMessages((prevMessages) => [
              ...prevMessages,
              {
                id: "user_tempid",
                role: "user",
                content: input,
                images: messageImages.length > 0 ? messageImages : undefined,
                files: useNewFileUpload && selectedFiles.length > 0 ? selectedFiles : undefined,
                metadata: {
                  includeWebResults: includeWebResults === true,
                  hasImages: messageImages.length > 0,
                  files: useNewFileUpload && selectedFiles.length > 0 ? selectedFiles : undefined,
                },
              },
            ]);
            handleAISubmit(e);
            // Clear files after submission
            if (useNewFileUpload && setSelectedFiles) {
              setSelectedFiles([]);
            }
          }}
          className={cn(
            "w-full max-w-none sm:max-w-[45rem] bg-background/95 backdrop-blur-sm border border-input rounded-2xl sm:rounded-[20px] px-2 sm:px-3 md:px-4 py-2 sm:py-2.5 md:py-3 flex items-center shadow-lg hover:shadow-xl transition-all duration-300 ease-out mb-4 sm:mb-6",
            (selectedImages.length > 0 || selectedFiles.length > 0) && "border-primary/40 shadow-primary/20 bg-primary/5"
          )}
        >
          {/* File/Image upload button */}
          <div className="mr-1.5 sm:mr-2 flex-shrink-0">
            {useNewFileUpload ? (
              <FileUpload
                onFilesSelected={handleFilesSelected}
                selectedFiles={selectedFiles}
                onRemoveFile={handleRemoveFile}
                disabled={status === "streaming" || status === "submitted"}
              />
            ) : (
              <ImageUpload
                onImagesSelected={handleImagesSelected}
                selectedImages={selectedImages}
                onRemoveImage={handleRemoveImage}
                disabled={status === "streaming" || status === "submitted"}
              />
            )}
          </div>

          <AutoResizeTextarea
            onKeyDown={handleKeyDown}
            onChange={(e: any) => handleChange(e)}
            value={input}
            placeholder={
              useNewFileUpload
                ? selectedFiles.length > 0
                  ? t("chat.enterMessageWithFiles") ||
                  "Ask about your files or add a message..."
                  : t("chat.enterMessage")
                : selectedImages.length > 0
                  ? t("chat.enterMessageWithImages") ||
                  "Ask about your images or add a message..."
                  : t("chat.enterMessage")
            }
            className="flex-1 bg-transparent text-sm sm:text-base md:text-base focus:outline-none px-1 sm:px-2 py-1 resize-none min-h-[32px] sm:min-h-[36px] max-h-[120px] sm:max-h-[160px] md:max-h-[200px]"
            disabled={status === "streaming" || status === "submitted"}
          />
          <Button
            size="icon"
            className={cn(
              "rounded-full flex-shrink-0 ml-1.5 sm:ml-2 transition-all duration-200",
              "h-7 w-7 sm:h-8 sm:w-8 md:h-9 md:w-9",
              canSubmit && status !== "streaming" && status !== "submitted"
                ? "bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg"
                : "bg-muted"
            )}
            disabled={
              !canSubmit || status === "streaming" || status === "submitted"
            }
          >
            {status === "streaming" || status === "submitted" ? (
              <Loader2 className="h-3 w-3 sm:h-4 sm:w-4 animate-spin" />
            ) : (
              <ArrowUpIcon className="h-3 w-3 sm:h-4 sm:w-4" />
            )}
          </Button>
        </form>
      </div>
    </div>
  );
};
